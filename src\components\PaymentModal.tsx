'use client'

import { useState, useEffect } from 'react'
import { X, Save, AlertCircle, User, DollarSign, Calendar, CreditCard, Users, FileText } from 'lucide-react'
import { useTheme } from 'next-themes'

import { VALIDATION_RULES } from '@/constants'
import { PAYMENT_METHODS, PaymentMethod } from '@/lib/supabase'
import { parseCurrencyInput } from '@/utils'

interface PaymentModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: () => void
  customerName?: string
  customerFamilyName?: string
}

export default function PaymentModal({ 
  isOpen, 
  onClose, 
  onSave, 
  customerName = '', 
  customerFamilyName = '' 
}: PaymentModalProps) {
  const { resolvedTheme } = useTheme()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Form state
  const [formData, setFormData] = useState({
    customer_name: customerName,
    customer_family_name: customerFamilyName,
    payment_amount: '',
    payment_date: new Date().toISOString().split('T')[0],
    payment_method: 'Cash' as PaymentMethod,
    responsible_family_member: '',
    notes: ''
  })

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Initialize form data when props change
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      customer_name: customerName,
      customer_family_name: customerFamilyName
    }))
    setErrors({})
    setError(null)
  }, [customerName, customerFamilyName, isOpen])

  // Validation function
  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    // Customer name validation
    if (!formData.customer_name.trim()) {
      newErrors.customer_name = 'Customer name is required'
    } else if (formData.customer_name.length < VALIDATION_RULES.debt.customerName.minLength) {
      newErrors.customer_name = `Customer name must be at least ${VALIDATION_RULES.debt.customerName.minLength} characters`
    } else if (formData.customer_name.length > VALIDATION_RULES.debt.customerName.maxLength) {
      newErrors.customer_name = `Customer name must not exceed ${VALIDATION_RULES.debt.customerName.maxLength} characters`
    }

    // Family name validation
    if (!formData.customer_family_name.trim()) {
      newErrors.customer_family_name = 'Family name is required'
    } else if (formData.customer_family_name.length < VALIDATION_RULES.debt.familyName.minLength) {
      newErrors.customer_family_name = `Family name must be at least ${VALIDATION_RULES.debt.familyName.minLength} characters`
    } else if (formData.customer_family_name.length > VALIDATION_RULES.debt.familyName.maxLength) {
      newErrors.customer_family_name = `Family name must not exceed ${VALIDATION_RULES.debt.familyName.maxLength} characters`
    }

    // Payment amount validation
    const amount = parseCurrencyInput(formData.payment_amount)
    if (!formData.payment_amount.trim()) {
      newErrors.payment_amount = 'Payment amount is required'
    } else if (isNaN(amount)) {
      newErrors.payment_amount = 'Payment amount must be a valid number'
    } else if (amount < VALIDATION_RULES.payment.paymentAmount.min) {
      newErrors.payment_amount = `Payment amount must be at least ₱${VALIDATION_RULES.payment.paymentAmount.min}`
    } else if (amount > VALIDATION_RULES.payment.paymentAmount.max) {
      newErrors.payment_amount = `Payment amount must not exceed ₱${VALIDATION_RULES.payment.paymentAmount.max.toLocaleString()}`
    }

    // Payment date validation
    if (!formData.payment_date) {
      newErrors.payment_date = 'Payment date is required'
    } else {
      const paymentDate = new Date(formData.payment_date)
      const today = new Date()
      today.setHours(23, 59, 59, 999) // End of today
      
      if (paymentDate > today) {
        newErrors.payment_date = 'Payment date cannot be in the future'
      }
    }

    // Responsible family member validation (optional but if provided, must meet requirements)
    if (formData.responsible_family_member.trim()) {
      if (formData.responsible_family_member.length < VALIDATION_RULES.payment.responsibleMember.minLength) {
        newErrors.responsible_family_member = `Responsible member name must be at least ${VALIDATION_RULES.payment.responsibleMember.minLength} characters`
      } else if (formData.responsible_family_member.length > VALIDATION_RULES.payment.responsibleMember.maxLength) {
        newErrors.responsible_family_member = `Responsible member name must not exceed ${VALIDATION_RULES.payment.responsibleMember.maxLength} characters`
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          customer_name: formData.customer_name.trim(),
          customer_family_name: formData.customer_family_name.trim(),
          payment_amount: parseCurrencyInput(formData.payment_amount),
          payment_date: formData.payment_date,
          payment_method: formData.payment_method,
          responsible_family_member: formData.responsible_family_member.trim() || null,
          notes: formData.notes.trim() || null
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save payment record')
      }

      onSave()
      onClose()
      
      // Reset form
      setFormData({
        customer_name: '',
        customer_family_name: '',
        payment_amount: '',
        payment_date: new Date().toISOString().split('T')[0],
        payment_method: 'Cash',
        responsible_family_member: '',
        notes: ''
      })
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save payment record')
    } finally {
      setLoading(false)
    }
  }

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    // Special handling for payment_amount to prevent precision issues
    if (field === 'payment_amount' && value) {
      // Ensure the value has at most 2 decimal places
      const decimalMatch = value.match(/^\d*\.?\d{0,2}$/)
      if (!decimalMatch) {
        return // Don't update if it would create more than 2 decimal places
      }
    }

    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div
        className="w-full max-w-2xl max-h-[90vh] overflow-y-auto rounded-2xl shadow-2xl"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b" style={{
          borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
        }}>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Record Payment
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Error Message */}
          {error && (
            <div className="flex items-center p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <AlertCircle className="h-5 w-5 text-red-500 mr-3 flex-shrink-0" />
              <p className="text-red-700 dark:text-red-300">{error}</p>
            </div>
          )}

          {/* Customer Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <User className="h-5 w-5 mr-2" />
              Customer Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="payment-customer-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Customer Name *
                </label>
                <input
                  id="payment-customer-name"
                  name="customer_name"
                  type="text"
                  value={formData.customer_name}
                  onChange={(e) => handleInputChange('customer_name', e.target.value)}
                  className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                    errors.customer_name
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500'
                  }`}
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                  placeholder="Enter customer name"
                  autoComplete="given-name"
                />
                {errors.customer_name && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.customer_name}</p>
                )}
              </div>

              <div>
                <label htmlFor="payment-customer-family-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Family Name *
                </label>
                <input
                  id="payment-customer-family-name"
                  name="customer_family_name"
                  type="text"
                  value={formData.customer_family_name}
                  onChange={(e) => handleInputChange('customer_family_name', e.target.value)}
                  className={`w-full px-4 py-3 rounded-lg border transition-colors ${
                    errors.customer_family_name
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500'
                  }`}
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                  placeholder="Enter family name"
                  autoComplete="family-name"
                />
                {errors.customer_family_name && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.customer_family_name}</p>
                )}
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              Payment Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="payment-amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Payment Amount (₱) *
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    id="payment-amount"
                    name="payment_amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.payment_amount}
                    onChange={(e) => handleInputChange('payment_amount', e.target.value)}
                    className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors ${
                      errors.payment_amount
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500'
                    }`}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                    placeholder="0.00"
                    autoComplete="off"
                  />
                </div>
                {errors.payment_amount && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.payment_amount}</p>
                )}
              </div>

              <div>
                <label htmlFor="payment-date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Payment Date *
                </label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    id="payment-date"
                    name="payment_date"
                    type="date"
                    value={formData.payment_date}
                    onChange={(e) => handleInputChange('payment_date', e.target.value)}
                    max={new Date().toISOString().split('T')[0]}
                    className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors ${
                      errors.payment_date
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                        : 'border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500'
                    }`}
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                      color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                    }}
                    autoComplete="off"
                  />
                </div>
                {errors.payment_date && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.payment_date}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="payment-method" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Payment Method *
              </label>
              <div className="relative">
                <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <select
                  id="payment-method"
                  name="payment_method"
                  value={formData.payment_method}
                  onChange={(e) => handleInputChange('payment_method', e.target.value)}
                  className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500 transition-colors"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                  autoComplete="off"
                >
                  {PAYMENT_METHODS.map((method) => (
                    <option key={method} value={method}>
                      {method}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Family Member Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Family Member Information
            </h3>

            <div>
              <label htmlFor="payment-responsible-member" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Responsible Family Member (Optional)
              </label>
              <div className="relative">
                <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  id="payment-responsible-member"
                  name="responsible_family_member"
                  type="text"
                  value={formData.responsible_family_member}
                  onChange={(e) => handleInputChange('responsible_family_member', e.target.value)}
                  className={`w-full pl-10 pr-4 py-3 rounded-lg border transition-colors ${
                    errors.responsible_family_member
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500'
                  }`}
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                  placeholder="Enter family member name who made the payment"
                  autoComplete="name"
                />
              </div>
              {errors.responsible_family_member && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.responsible_family_member}</p>
              )}
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Specify which family member made this payment (e.g., &quot;Maria&quot;, &quot;Juan Jr.&quot;, etc.)
              </p>
            </div>

            <div>
              <label htmlFor="payment-notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Notes (Optional)
              </label>
              <div className="relative">
                <FileText className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <textarea
                  id="payment-notes"
                  name="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  rows={3}
                  className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 focus:border-green-500 focus:ring-green-500 transition-colors resize-none"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                  }}
                  placeholder="Add any additional notes about this payment..."
                  autoComplete="off"
                />
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-3 pt-6 border-t" style={{
            borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={loading}
              className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg hover:from-green-700 hover:to-emerald-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Recording...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Record Payment
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
