'use client'

import { useState } from 'react'
import { useTheme } from 'next-themes'
import {
  User,
  MapPin,
  Phone,
  Save,
  X,
  Cake,
  Home,
  FileText
} from 'lucide-react'

import ProfilePictureUpload from './ProfilePictureUpload'
import { Customer } from '@/lib/supabase'

interface CustomerProfileFormProps {
  customer?: Partial<Customer>
  onSave: (customerData: Partial<Customer>) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
  title?: string
}

export default function CustomerProfileForm({
  customer,
  onSave,
  onCancel,
  isLoading = false,
  title = 'Customer Profile'
}: CustomerProfileFormProps) {
  const { resolvedTheme } = useTheme()
  const [formData, setFormData] = useState({
    customer_name: customer?.customer_name || '',
    customer_family_name: customer?.customer_family_name || '',
    phone_number: customer?.phone_number || '',
    address: customer?.address || '',
    birth_date: customer?.birth_date || '',
    birth_place: customer?.birth_place || '',
    profile_picture_url: customer?.profile_picture_url || '',
    profile_picture_public_id: customer?.profile_picture_public_id || '',
    notes: customer?.notes || ''
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.customer_name.trim()) {
      newErrors.customer_name = 'First name is required'
    }

    if (!formData.customer_family_name.trim()) {
      newErrors.customer_family_name = 'Last name is required'
    }

    if (formData.phone_number && !/^[0-9+\-\s()]+$/.test(formData.phone_number)) {
      newErrors.phone_number = 'Invalid phone number format'
    }

    if (formData.birth_date && new Date(formData.birth_date) > new Date()) {
      newErrors.birth_date = 'Birth date cannot be in the future'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    try {
      await onSave(formData)
    } catch (error) {
      console.error('Error saving customer:', error)
    }
  }

  const handleImageChange = (imageUrl: string | null, publicId: string | null) => {
    setFormData(prev => ({
      ...prev,
      profile_picture_url: imageUrl || '',
      profile_picture_public_id: publicId || ''
    }))
  }

  return (
    <div 
      className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border max-w-4xl mx-auto"
      style={{
        borderColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb'
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold" style={{
          color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
        }}>
          {title}
        </h2>
        <button
          onClick={onCancel}
          disabled={isLoading}
          className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50"
        >
          <X className="h-6 w-6" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Profile Picture and Basic Info */}
        <div className="flex flex-col md:flex-row md:items-start md:space-x-6 space-y-4 md:space-y-0">
          <div className="flex-shrink-0">
            <ProfilePictureUpload
              currentImageUrl={formData.profile_picture_url}
              onImageChange={handleImageChange}
              size="xl"
              disabled={isLoading}
            />
          </div>

          <div className="flex-1 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <User className="h-4 w-4 inline mr-1" />
                  First Name *
                </label>
                <input
                  id="customer-first-name"
                  name="customer_name"
                  type="text"
                  value={formData.customer_name}
                  onChange={(e) => setFormData(prev => ({ ...prev, customer_name: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg ${errors.customer_name ? 'border-red-500' : ''}`}
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    borderColor: errors.customer_name ? '#ef4444' : (resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'),
                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                  }}
                  placeholder="Enter first name"
                  disabled={isLoading}
                  autoComplete="given-name"
                />
                {errors.customer_name && (
                  <p className="text-red-500 text-sm mt-1">{errors.customer_name}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Last Name *
                </label>
                <input
                  id="customer-last-name"
                  name="customer_family_name"
                  type="text"
                  value={formData.customer_family_name}
                  onChange={(e) => setFormData(prev => ({ ...prev, customer_family_name: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg ${errors.customer_family_name ? 'border-red-500' : ''}`}
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                    borderColor: errors.customer_family_name ? '#ef4444' : (resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'),
                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                  }}
                  placeholder="Enter last name"
                  disabled={isLoading}
                  autoComplete="family-name"
                />
                {errors.customer_family_name && (
                  <p className="text-red-500 text-sm mt-1">{errors.customer_family_name}</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Phone className="h-4 w-4 inline mr-1" />
              Phone Number
            </label>
            <input
              id="customer-phone"
              name="phone_number"
              type="tel"
              value={formData.phone_number}
              onChange={(e) => setFormData(prev => ({ ...prev, phone_number: e.target.value }))}
              className={`w-full px-3 py-2 border rounded-lg ${errors.phone_number ? 'border-red-500' : ''}`}
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: errors.phone_number ? '#ef4444' : (resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'),
                color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
              }}
              placeholder="Enter phone number"
              disabled={isLoading}
              autoComplete="tel"
            />
            {errors.phone_number && (
              <p className="text-red-500 text-sm mt-1">{errors.phone_number}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <MapPin className="h-4 w-4 inline mr-1" />
              Address
            </label>
            <input
              id="customer-address"
              name="address"
              type="text"
              value={formData.address}
              onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
              className="w-full px-3 py-2 border rounded-lg"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
                color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
              }}
              placeholder="Enter address"
              disabled={isLoading}
              autoComplete="street-address"
            />
          </div>
        </div>

        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Cake className="h-4 w-4 inline mr-1" />
              Birth Date
            </label>
            <input
              id="customer-birth-date"
              name="birth_date"
              type="date"
              value={formData.birth_date}
              onChange={(e) => setFormData(prev => ({ ...prev, birth_date: e.target.value }))}
              className={`w-full px-3 py-2 border rounded-lg ${errors.birth_date ? 'border-red-500' : ''}`}
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: errors.birth_date ? '#ef4444' : (resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db'),
                color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
              }}
              disabled={isLoading}
              autoComplete="bday"
            />
            {errors.birth_date && (
              <p className="text-red-500 text-sm mt-1">{errors.birth_date}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Home className="h-4 w-4 inline mr-1" />
              Birth Place
            </label>
            <input
              id="customer-birth-place"
              name="birth_place"
              type="text"
              value={formData.birth_place}
              onChange={(e) => setFormData(prev => ({ ...prev, birth_place: e.target.value }))}
              className="w-full px-3 py-2 border rounded-lg"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
                color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
              }}
              placeholder="Enter birthplace"
              disabled={isLoading}
              autoComplete="off"
            />
          </div>
        </div>

        {/* Notes */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <FileText className="h-4 w-4 inline mr-1" />
            Notes
          </label>
          <textarea
            id="customer-notes"
            name="notes"
            value={formData.notes}
            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            rows={4}
            className="w-full px-3 py-2 border rounded-lg resize-none"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
              borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
              color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
            }}
            placeholder="Add notes about this customer..."
            disabled={isLoading}
            autoComplete="off"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 pt-6 border-t" style={{
          borderColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb'
        }}>
          <button
            type="button"
            onClick={onCancel}
            disabled={isLoading}
            className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center space-x-2 transition-colors"
          >
            <Save className="h-4 w-4" />
            <span>{isLoading ? 'Saving...' : 'Save Profile'}</span>
          </button>
        </div>
      </form>
    </div>
  )
}
